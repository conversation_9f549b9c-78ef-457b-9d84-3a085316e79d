import 'package:flutter/material.dart';
import 'linked_list_operations_page.dart';
import 'stack_queue_page.dart';
import 'tree_operations_page.dart';

class DataStructuresPage extends StatelessWidget {
  const DataStructuresPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Structures'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildDataStructureCard(
              context,
              title: 'Linked Lists',
              description: 'Singly, Doubly, and Circular linked lists',
              icon: Icons.link,
              color: Colors.blue,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LinkedListOperationsPage(),
                  ),
                );
              },
            ),
            _buildDataStructureCard(
              context,
              title: 'Stack & Queue',
              description: 'LIFO and FIFO data structures',
              icon: Icons.layers,
              color: Colors.green,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const StackQueuePage(),
                  ),
                );
              },
            ),
            _buildDataStructureCard(
              context,
              title: 'Trees',
              description: 'Binary trees and operations',
              icon: Icons.account_tree,
              color: Colors.orange,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const TreeOperationsPage(),
                  ),
                );
              },
            ),
            _buildDataStructureCard(
              context,
              title: 'Graphs',
              description: 'Coming soon...',
              icon: Icons.hub,
              color: Colors.purple,
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Coming soon!')),
                );
              },
            ),
            _buildDataStructureCard(
              context,
              title: 'Hash Tables',
              description: 'Coming soon...',
              icon: Icons.tag,
              color: Colors.red,
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Coming soon!')),
                );
              },
            ),
            _buildDataStructureCard(
              context,
              title: 'Heaps',
              description: 'Coming soon...',
              icon: Icons.trending_up,
              color: Colors.teal,
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Coming soon!')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataStructureCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
