import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';

class UserService {
  static const _storage = FlutterSecureStorage();
  static const String _userKey = 'current_user';
  static const String _emailKey = 'user_email';
  static const String _nameKey = 'user_name';

  // Get current user from storage
  Future<UserModel?> getCurrentUser() async {
    try {
      final email = await _storage.read(key: _emailKey);
      final name = await _storage.read(key: _nameKey);
      
      if (email != null) {
        return UserModel(
          id: email.hashCode.toString(),
          email: email,
          name: name ?? 'User',
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Login user (simplified - in real app would connect to backend)
  Future<UserModel> loginUser(String email, String password) async {
    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Simple validation (in real app would validate against backend)
    if (email.isNotEmpty && password.length >= 6) {
      final user = UserModel(
        id: email.hashCode.toString(),
        email: email,
        name: email.split('@')[0],
      );
      
      // Store user data
      await _storage.write(key: _emailKey, value: email);
      await _storage.write(key: _nameKey, value: user.name);
      
      return user;
    } else {
      throw Exception('Invalid credentials');
    }
  }

  // Register user (simplified)
  Future<UserModel> registerUser(String email, String password, {String? name}) async {
    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Simple validation
    if (email.isNotEmpty && password.length >= 6) {
      final user = UserModel(
        id: email.hashCode.toString(),
        email: email,
        name: name ?? email.split('@')[0],
      );
      
      // Store user data
      await _storage.write(key: _emailKey, value: email);
      await _storage.write(key: _nameKey, value: user.name);
      
      return user;
    } else {
      throw Exception('Invalid registration data');
    }
  }

  // Update user profile
  Future<UserModel> updateUserProfile(String name, String? photoUrl) async {
    final email = await _storage.read(key: _emailKey);
    if (email == null) {
      throw Exception('No user logged in');
    }
    
    // Update stored name
    await _storage.write(key: _nameKey, value: name);
    
    return UserModel(
      id: email.hashCode.toString(),
      email: email,
      name: name,
      photoUrl: photoUrl,
    );
  }

  // Logout user
  Future<void> logout() async {
    await _storage.delete(key: _emailKey);
    await _storage.delete(key: _nameKey);
    await _storage.delete(key: _userKey);
  }
}
